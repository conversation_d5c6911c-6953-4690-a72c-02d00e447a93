package com.lgjy.system.api;

import com.alibaba.fastjson2.JSONObject;
import com.lgjy.common.core.constant.SecurityConstants;
import com.lgjy.common.core.constant.ServiceNameConstants;
import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.factory.RemoteGateFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(contextId = "RemoteGateService", value = ServiceNameConstants.GATE_SERVICE, fallbackFactory = RemoteGateFallbackFactory.class)
public interface RemoteGateService {
        /**
         * 根据场库和车牌号查询缴费信息
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/search/findParkingRate")
        R<String> findParkingRate(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 临停车缴费通知
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/payOrder")
        R<String> payOrder(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 道闸出口查询
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/search/outPayQuery")
        R<String> outPayQuery(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 无牌车入场
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/noPlateIn")
        R<String> noPlateIn(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 保存月租车
         */
        @PostMapping("/control/saveMonthCar")
        R<String> saveMonthCar(@RequestBody JSONObject requestData,
                              @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 删除月租车
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/delMonthCar")
        R<String> delMonthCar(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 保存免费车
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/saveFreeCar")
        R<String> saveFreeCar(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 保存黑名单车辆
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/saveBlackCar")
        R<String> saveBlackCar(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

        /**
         * 删除黑名单车辆
         *
         * @param requestData 请求数据JSON对象
         * @param source 请求来源
         * @return
         */
        @PostMapping("/control/delBlackCar")
        R<String> delBlackCar(@RequestBody JSONObject requestData,
                        @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
