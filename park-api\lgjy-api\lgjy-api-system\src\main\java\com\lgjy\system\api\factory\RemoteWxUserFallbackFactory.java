package com.lgjy.system.api.factory;

import com.lgjy.common.core.domain.R;
import com.lgjy.system.api.RemoteWxUserService;
import com.lgjy.system.api.domain.WxUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 小程序用户服务降级处理
 *
 * <AUTHOR>
 */
@Component
public class RemoteWxUserFallbackFactory implements FallbackFactory<RemoteWxUserService> {
    private static final Logger log = LoggerFactory.getLogger(RemoteUserFallbackFactory.class);

    @Override
    public RemoteWxUserService create(Throwable throwable)
    {
        log.error("用户服务调用失败:{}", throwable.getMessage());
        return new RemoteWxUserService()
        {
            @Override
            public R<WxUser> getUserInfo(String username, String source)
            {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<?> insertUser(WxUser wxUser, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
            @Override
            public R<WxUser> getUserInfoByOpenId(String openid, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }

            @Override
            public R<?> updateUserInfo(WxUser wxUser, String source) {
                return R.fail("获取用户失败:" + throwable.getMessage());
            }
        };
    }
}
