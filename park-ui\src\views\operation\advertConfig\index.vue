<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryRef"
      :inline="true"
      v-show="showSearch"
    >
      <el-form-item label="广告标题" prop="advertTitle">
        <el-input
          v-model="queryParams.advertTitle"
          placeholder="请输入广告标题"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select
          v-model="queryParams.status"
          placeholder="广告状态"
          clearable
          style="width: 200px"
        >
          <el-option
            v-for="dict in advert_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['system:advertConfig:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:advertConfig:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:advertConfig:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['system:advertConfig:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="advertConfigList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="广告ID" align="center" prop="id" /> -->
      <el-table-column
        label="广告标题"
        align="center"
        prop="advertTitle"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        label="图片"
        align="center"
        prop="picUrl"
        width="100"
      >
        <template #default="scope">
          <image-preview
            :src="scope.row.picUrl"
            :width="50"
            :height="50"
            v-if="scope.row.picUrl"
          />
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="跳转链接"
        align="center"
        prop="url"
      /> -->
      <el-table-column
        label="备注"
        align="center"
        prop="remark"
        :show-overflow-tooltip="true"
      />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="advert_status" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
        align="center"
        prop="createTime"
        width="180"
      >
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="180"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            link
            type="primary"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:advertConfig:edit']"
            >修改</el-button
          >
          <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:advertConfig:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <custom-pagination
      :total="total"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改广告配置信息对话框 -->
    <el-dialog
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        ref="advertConfigRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="广告标题" prop="advertTitle">
          <el-input v-model="form.advertTitle" placeholder="请输入广告标题" />
        </el-form-item>
        <!-- <el-form-item label="跳转链接" prop="url">
          <el-input v-model="form.url" placeholder="请输入跳转链接" />
        </el-form-item> -->
        <el-form-item label="图片地址" prop="picUrl">
          <image-upload
            v-model="form.picUrl"
            :limit="1"
            :action="'/file/upload/path'"
            :data="{ path: 'advertise' }"
            :file-size="5"
            :file-type="['png', 'jpg', 'jpeg', 'gif', 'webp']"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in advert_status"
              :key="dict.value"
              :value="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="AdvertConfig">
import {
  listAdvertConfig,
  getAdvertConfig,
  delAdvertConfig,
  addAdvertConfig,
  updateAdvertConfig,
} from "@/api/system/advertConfig";
import CustomPagination from "@/components/CustomPagination/index.vue";

// 注册组件
defineOptions({
  components: {
    CustomPagination,
  },
});

const { proxy } = getCurrentInstance();
const { advert_status } = proxy.useDict("advert_status");

const advertConfigList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    advertTitle: undefined,
    status: undefined,
  },
  rules: {
    advertTitle: [
      { required: true, message: "广告标题不能为空", trigger: "blur" },
    ],
    picUrl: [
      { required: true, message: "图片不能为空", trigger: "change" },
    ],
    status: [{ required: true, message: "状态不能为空", trigger: "change" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询广告配置信息列表 */
function getList() {
  loading.value = true;
  listAdvertConfig(queryParams.value).then((response) => {
    advertConfigList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    advertTitle: undefined,
    url: undefined,
    picUrl: undefined,
    status: 1,
    remark: undefined,
  };
  proxy.resetForm("advertConfigRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加广告配置信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getAdvertConfig(id).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改广告配置信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["advertConfigRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateAdvertConfig(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAdvertConfig(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const advertIds = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除广告配置信息编号为"' + advertIds + '"的数据项？')
    .then(function () {
      return delAdvertConfig(advertIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
    "system/advertConfig/export",
    {
      ...queryParams.value,
    },
    `advertConfig_${new Date().getTime()}.xlsx`
  );
}

getList();
</script>
