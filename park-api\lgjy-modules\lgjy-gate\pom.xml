<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>lgjy-modules</artifactId>
        <groupId>com.lgjy</groupId>
        <version>3.6.6</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>lgjy-modules-gate</artifactId>

    <description>
        lgjy-gate道闸设备控制微服务模块
    </description>

    <dependencies>
        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- SpringBoot Actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>



        <!-- 安全模块 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-common-security</artifactId>
        </dependency>

        <!-- 权限范围 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-common-datascope</artifactId>
        </dependency>

        <!-- 日志记录 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-common-log</artifactId>
        </dependency>



        <!-- 分布式事务 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-common-seata</artifactId>
        </dependency>

        <!-- 核心模块 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-common-core</artifactId>
        </dependency>

        <!-- 系统接口 -->
        <dependency>
            <groupId>com.lgjy</groupId>
            <artifactId>lgjy-api-system</artifactId>
        </dependency>

        <!-- Web启动器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- MySQL驱动 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>

        <!-- FastJSON -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- Apache Commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>

        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Docker Maven Plugin -->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <!-- 支持profile控制Docker构建 -->
                    <skip>${docker.skip}</skip>
                    <skipBuild>${docker.skipBuild}</skipBuild>
                    <skipPush>${docker.skipPush}</skipPush>
                    <images>
                        <image>
                            <name>${docker.image.prefix}/${project.artifactId}:${project.version}</name>
                            <build>
                                <from>${docker.base.image}</from>
                                <assembly>
                                    <descriptorRef>artifact-with-dependencies</descriptorRef>
                                    <targetDir>/app</targetDir>
                                </assembly>
                                <cmd>java -jar /app/${project.artifactId}.jar</cmd>
                                <ports>
                                    <port>9203</port>
                                </ports>
                                <env>
                                    <JAVA_OPTS>-Xms256m -Xmx512m -Dspring.profiles.active=prod</JAVA_OPTS>
                                </env>
                            </build>
                        </image>
                    </images>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>