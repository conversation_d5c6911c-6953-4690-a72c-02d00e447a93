<template>
    <view class="mine-edit-container">
        <!-- 用户信息编辑卡片 -->
        <view class="edit-card">
            <!-- 头像编辑 -->
            <view class="edit-item-avatar">
                <view class="edit-label-avatar">我的头像</view>
                <button class="avatar-btn" open-type="chooseAvatar" @chooseavatar="chooseavatar">
                    <image class="avatar-image" :src="userEdit.img || '/static/mine/avatar.png'" mode="aspectFill">
                    </image>
                </button>
            </view>

            <!-- 昵称编辑 -->
            <view class="edit-item">
                <view class="edit-label">我的昵称</view>
                <view class="input-wrapper">
                    <u-input v-model="userEdit.nickName" type="nickname" border="none" placeholder="请输入昵称" clearable>
                    </u-input>
                    <u-icon size="18" color="#9e9e9e" name="edit-pen"></u-icon>
                </view>
            </view>

            <!-- 手机号码编辑 -->
            <view class="edit-item">
                <view class="edit-label">手机号码</view>
                <view class="phone-wrapper">
                    <button class="phone-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">
                        <text class="phone-text">{{ userEdit.phoneNumber || '请选择手机号码' }}</text>
                    </button>
                    <u-icon size="18" color="#9e9e9e" name="edit-pen"></u-icon>
                </view>
            </view>
        </view>

        <!-- 保存按钮 -->
        <view class="save-section">
            <button @tap="handleSave" class="save-btn">保存</button>
        </view>
    </view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';
import { getPhoneNumberByCode, updateUserInfo } from '@/api/user';

const userInfo = ref(null);
const isLogin = ref(false);

const userEdit = ref({
    img: '',
    nickName: '',
    phoneNumber: ''
});

onLoad(() => {
    userInfo.value = uni.getStorageSync('wxUser');
    console.log(userInfo.value);

    // 初始化编辑数据
    if (userInfo.value) {
        userEdit.value.img = userInfo.value.img || '';
        userEdit.value.nickName = userInfo.value.nickName || '';
        userEdit.value.phoneNumber = userInfo.value.phoneNumber || '';
    }

    if (uni.getStorageSync('token')) {
        isLogin.value = true;
    }
});

// 选择头像
const chooseavatar = (e) => {
    console.log(e);
    // 获取选择的头像URL
    const avatarUrl = e.detail.avatarUrl;
    if (avatarUrl) {
        userEdit.value.img = avatarUrl;
    }
};

// 获取手机号码
const getPhoneNumber = (e) => {
    const phoneCode = e.detail.code;
    if (phoneCode) {
        getPhoneNumberByCode(phoneCode).then(res => {
            userEdit.value.phoneNumber = res.data
        })
    } else {
        console.log('验证手机号失败')
    }
};

// 保存用户信息
const handleSave = async () => {
    // 获取系统缓存中的用户信息
    const cachedUserInfo = uni.getStorageSync('wxUser') || {};

    // 检查是否有变化
    const hasChanges = (
        (userEdit.value.img && userEdit.value.img !== cachedUserInfo.img) ||
        userEdit.value.nickName !== cachedUserInfo.nickName ||
        userEdit.value.phoneNumber !== cachedUserInfo.phoneNumber
    );

    if (!hasChanges) {
        uni.showToast({
            title: '信息未更新',
            icon: 'none',
            duration: 2000
        });
        return;
    }

    // 显示加载提示
    uni.showLoading({
        title: '保存中...',
        mask: true
    });

    try {
        // 如果有新的头像（临时文件），先上传头像
        let finalImgUrl = userEdit.value.img;
        if (userEdit.value.img && userEdit.value.img.startsWith('http://tmp/')) {
            try {
                // 上传头像文件
                const uploadResult = await uploadAvatar(userEdit.value.img);
                finalImgUrl = uploadResult;
                console.log('头像上传成功:', finalImgUrl);
            } catch (error) {
                console.error('头像上传失败:', error);
                uni.showToast({
                    title: '头像上传失败',
                    icon: 'none',
                    duration: 2000
                });
                return;
            }
        }

        // 准备保存的数据
        const saveData = {
            img: finalImgUrl,
            nickName: userEdit.value.nickName,
            phoneNumber: userEdit.value.phoneNumber,
            userName: userEdit.value.phoneNumber
        };

        // 这里调用后端接口更新用户信息
        const res = await updateUserInfo(saveData);

        uni.setStorageSync('token', res.data.token)
        uni.setStorageSync('wxUser', res.data.wxUser)

        uni.showToast({
            title: '保存成功',
            icon: 'success',
            duration: 2000
        });

        // 延迟返回上一页
        setTimeout(() => {
            uni.navigateBack();
        }, 1500);

    } catch (error) {
        console.error('保存失败:', error);
        uni.showToast({
            title: '保存失败，请重试',
            icon: 'none',
            duration: 2000
        });
    } finally {
        uni.hideLoading();
    }
};

// 上传头像文件
const uploadAvatar = (filePath) => {
    return new Promise((resolve, reject) => {
        uni.uploadFile({
            url: 'http://localhost:8080/file/upload/path',
            filePath: filePath,
            name: 'file',
            formData: {
                path: 'wxAvatar'
            },
            header: {
                'Authorization': 'WxBearer ' + (uni.getStorageSync('token') || '')
            },
            success: (uploadRes) => {
                try {
                    const result = JSON.parse(uploadRes.data);
                    if (result.code === 200) {
                        resolve(result.data.url);
                    } else {
                        reject(new Error(result.msg || '上传失败'));
                    }
                } catch (error) {
                    reject(new Error('解析响应失败'));
                }
            },
            fail: (error) => {
                reject(error);
            }
        });
    });
};
</script>

<style lang="scss" scoped>
.mine-edit-container {
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 24rpx;
}

// 编辑卡片
.edit-card {
    background-color: #fff;
    border-radius: 24rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;
}

// 头像编辑
.edit-item-avatar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0;
    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

    .edit-label-avatar {
        font-size: 32rpx;
        font-weight: bold;
        color: #000000;
    }

    .avatar-btn {
        width: 120rpx;
        height: 120rpx;
        border: none;
        background: transparent;
        padding: 0;
        margin: 0;
        border-radius: 50%;
        overflow: hidden;
    }

    .avatar-image {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        border: 4rpx solid #f0f0f0;
    }
}

.edit-item {
    padding: 24rpx 0;
    border-bottom: 2rpx solid rgba(189, 189, 189, 0.2);

    .edit-label {
        font-size: 32rpx;
        font-weight: bold;
        color: #000000;
        margin-bottom: 25rpx;
    }

    .input-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 20rpx 0 15rpx;
    }

    .phone-wrapper {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .phone-btn {
        background: transparent;
        padding: 0;
        margin: 0;
        font-size: 32rpx;
        color: #333333;
        line-height: 42rpx;
        border: none;
    }

    .phone-text {
        color: #000000;
    }
}

.input-wrapper {
    width: 100%;
}

.save-section {
    padding-top: 20rpx;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #246bfd 0%, #6f9eff 100%);
    color: #fff;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: bold;
    border: none;
}
</style>
