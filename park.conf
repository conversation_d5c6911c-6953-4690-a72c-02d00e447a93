# 上游后端API服务器配置
upstream park_api_backend {
    server 127.0.0.1:8080;  # 网关服务地址
    # 备用网关实例（如果有的话）
    # server 127.0.0.1:8081 backup;
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name test-parknew.lgfw24hours.com localhost;

    # 重定向所有HTTP请求到HTTPS
    return 301 https://$host:3443$request_uri;
}

# HTTPS主配置
server {
    listen       443 ssl;
    listen [::]:443 ssl;
    server_name  test-parknew.lgfw24hours.com;
    client_max_body_size 500m;

    # 停车系统专用证书
    ssl_certificate /home/<USER>/ssl/test-parknew.lgfw24hours.com.pem;
    ssl_certificate_key /home/<USER>/ssl/test-parknew.lgfw24hours.com.key;

    ssl_session_cache    shared:SSL:1m;
    ssl_session_timeout  5m;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;

    # 安全头设置
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains;";
    add_header X-Permitted-Cross-Domain-Policies "none";
    add_header X-Frame-Options "DENY";
    add_header X-Download-Options "noopen" always;

    # 域名验证 - 仅停车系统域名
    if ($http_host !~* ^(test-parknew\.lgfw24hours\.com\:3443|test-parknew\.lgfw24hours\.com|localhost|localhost\:443)$) {
        return 403;
    }

    # 微信小程序校验文件配置 - 临时测试用
    location /qr/ {
        alias /home/<USER>/frontend/dist/qr/;

        # 允许访问txt文件
        location ~* \.txt$ {
            add_header Content-Type "text/plain; charset=utf-8";
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";

            # 允许跨域访问（微信验证需要）
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type";

            # 处理OPTIONS预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Length 0;
                return 204;
            }
        }

        # 安全设置 - 只允许访问txt文件
        location ~ \.(php|jsp|asp|aspx|sh|pl|py|js|html|htm)$ {
            deny all;
        }

        # 禁止目录浏览
        autoindex off;

        # 访问日志（可选，用于调试）
        access_log /var/log/nginx/wechat-verify.access.log;
    }



    # Nacos代理配置
    location ^~ /nacos/ {
        # 代理到Docker映射的端口
        proxy_pass http://127.0.0.1:8848/nacos/;

        # Nacos反向代理必需的头部设置
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Port 3443;
        proxy_set_header X-Forwarded-Ssl on;

        # 认证头部传递
        proxy_pass_request_headers on;
        proxy_set_header Authorization $http_authorization;

        # Nacos特殊配置
        proxy_buffering off;
        proxy_request_buffering off;
        proxy_http_version 1.1;

        # WebSocket支持（Nacos控制台需要）
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;

        # 允许大文件上传
        client_max_body_size 100m;

        # 禁用代理重定向重写
        proxy_redirect off;
    }

    # Nacos根路径重定向
    location = /nacos {
        return 301 $scheme://$server_name/nacos/;
    }

    # 前端静态文件根目录
    root /home/<USER>/frontend/dist;
    index index.html;

    # Gzip压缩配置
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # API代理配置
    location /api/ {
        proxy_pass http://park_api_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;

        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;

        # CORS处理（如果需要）
        add_header 'Access-Control-Allow-Origin' '$http_origin' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
        add_header 'Access-Control-Allow-Credentials' 'true' always;

        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # 上传文件静态访问配置
    location /statics/ {
        alias /home/<USER>/images/;

        # 安全设置 - 禁止执行脚本文件
        location ~ \.(php|jsp|asp|aspx|sh|pl|py)$ {
            deny all;
        }

        # 允许的文件类型和缓存设置
        location ~* \.(jpg|jpeg|png|gif|bmp|ico|svg|webp|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar|mp4|avi|mov)$ {
            expires 30d;
            add_header Cache-Control "public, immutable";
            add_header Vary Accept-Encoding;
            access_log off;
        }

        # 防止直接访问目录
        autoindex off;

        # 大文件支持
        client_max_body_size 100m;
    }

    # 静态资源缓存配置
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }

    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }

    # Vue.js SPA路由支持 - 关键配置
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 错误页面
    error_page 404 /index.html;
    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /home/<USER>/frontend/dist;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 访问日志
    access_log /var/log/nginx/park-frontend.access.log;
    error_log /var/log/nginx/park-frontend.error.log;
}
