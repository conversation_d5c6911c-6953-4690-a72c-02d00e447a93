package com.lgjy.file.utils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Objects;
import javax.imageio.ImageIO;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.multipart.MultipartFile;
import com.lgjy.common.core.exception.file.FileException;
import com.lgjy.common.core.exception.file.FileNameLengthLimitExceededException;
import com.lgjy.common.core.exception.file.FileSizeLimitExceededException;
import com.lgjy.common.core.exception.file.InvalidExtensionException;
import com.lgjy.common.core.utils.DateUtils;
import com.lgjy.common.core.utils.StringUtils;
import com.lgjy.common.core.utils.file.FileTypeUtils;
import com.lgjy.common.core.utils.file.MimeTypeUtils;
import com.lgjy.common.core.utils.uuid.Seq;

/**
 * 文件上传工具类
 * 
 * <AUTHOR>
 */
public class FileUploadUtils
{
    /**
     * 默认大小 50M
     */
    public static final long DEFAULT_MAX_SIZE = 50 * 1024 * 1024L;

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * 根据文件路径上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @return 文件名称
     * @throws IOException
     */
    public static final String upload(String baseDir, MultipartFile file) throws IOException
    {
        try
        {
            return upload(baseDir, file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        }
        catch (FileException fe)
        {
            throw new IOException(fe.getDefaultMessage(), fe);
        }
        catch (Exception e)
        {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param baseDir 相对应用的基目录
     * @param file 上传的文件
     * @param allowedExtension 上传文件类型
     * @return 返回上传成功的文件名
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException 比如读写文件出错时
     * @throws InvalidExtensionException 文件校验异常
     */
    public static final String upload(String baseDir, MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException,
            InvalidExtensionException
    {
        int fileNamelength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH)
        {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }

        assertAllowed(file, allowedExtension);

        // 文件内容安全校验
        validateFileContent(file);

        String fileName = extractFilename(file);

        String absPath = getAbsoluteFile(baseDir, fileName).getAbsolutePath();
        file.transferTo(Paths.get(absPath));
        return getPathFileName(fileName);
    }

    /**
     * 编码文件名
     */
    public static final String extractFilename(MultipartFile file)
    {
        return StringUtils.format("{}/{}_{}.{}", DateUtils.datePath(),
                FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getId(Seq.uploadSeqType), FileTypeUtils.getExtension(file));
    }

    /**
     * 文件内容安全校验
     *
     * @param file 上传的文件
     * @throws InvalidExtensionException 文件内容校验异常
     * @throws IOException IO异常
     */
    public static final void validateFileContent(MultipartFile file) throws InvalidExtensionException, IOException
    {
        String extension = FileTypeUtils.getExtension(file).toLowerCase();

        // 检查是否为符号链接（通过文件名特征）
        String originalFilename = file.getOriginalFilename();
        if (originalFilename != null && (originalFilename.contains("../") || originalFilename.contains("..\\") || originalFilename.startsWith("/")))
        {
            throw new InvalidExtensionException(new String[]{extension}, extension, originalFilename);
        }

        // 图片文件内容校验
        if (isImageFile(extension))
        {
            validateImageContent(file);
        }

        // 文档文件内容校验
        if (isDocumentFile(extension))
        {
            validateDocumentContent(file);
        }

        // 检查文件头部是否包含脚本特征
        validateFileHeader(file);
    }

    /**
     * 判断是否为图片文件
     */
    private static boolean isImageFile(String extension)
    {
        String[] imageExtensions = {"jpg", "jpeg", "png", "gif", "bmp", "webp", "ico", "svg"};
        return Arrays.asList(imageExtensions).contains(extension);
    }

    /**
     * 判断是否为文档文件
     */
    private static boolean isDocumentFile(String extension)
    {
        String[] docExtensions = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
        return Arrays.asList(docExtensions).contains(extension);
    }

    /**
     * 校验图片文件内容
     */
    private static void validateImageContent(MultipartFile file) throws InvalidExtensionException, IOException
    {
        try (InputStream inputStream = file.getInputStream())
        {
            // 使用ImageIO验证图片文件的真实性
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null)
            {
                throw new InvalidExtensionException(new String[]{"image"},
                    FileTypeUtils.getExtension(file), file.getOriginalFilename());
            }

            // 检查图片尺寸是否合理（防止过大的图片攻击）
            if (image.getWidth() > 10000 || image.getHeight() > 10000)
            {
                throw new InvalidExtensionException(new String[]{"image"},
                    FileTypeUtils.getExtension(file), file.getOriginalFilename());
            }
        }
        catch (Exception e)
        {
            if (e instanceof InvalidExtensionException)
            {
                throw e;
            }
            throw new InvalidExtensionException(new String[]{"image"},
                FileTypeUtils.getExtension(file), file.getOriginalFilename());
        }
    }

    /**
     * 校验文档文件内容
     */
    private static void validateDocumentContent(MultipartFile file) throws InvalidExtensionException, IOException
    {
        try (InputStream inputStream = file.getInputStream())
        {
            byte[] header = new byte[1024];
            int bytesRead = inputStream.read(header);

            if (bytesRead > 0)
            {
                String headerStr = new String(header, 0, Math.min(bytesRead, 100)).toLowerCase();

                // 检查是否包含脚本标签
                if (headerStr.contains("<script") || headerStr.contains("<?php") ||
                    headerStr.contains("<%") || headerStr.contains("#!/"))
                {
                    throw new InvalidExtensionException(new String[]{"document"},
                        FileTypeUtils.getExtension(file), file.getOriginalFilename());
                }
            }
        }
    }

    /**
     * 校验文件头部
     */
    private static void validateFileHeader(MultipartFile file) throws InvalidExtensionException, IOException
    {
        try (InputStream inputStream = file.getInputStream())
        {
            byte[] header = new byte[512];
            int bytesRead = inputStream.read(header);

            if (bytesRead > 0)
            {
                String headerStr = new String(header, 0, Math.min(bytesRead, 100)).toLowerCase();

                // 检查常见的恶意文件特征
                String[] maliciousPatterns = {
                    "<?php", "<%", "<script", "#!/bin/", "#!/usr/bin/",
                    "eval(", "exec(", "system(", "shell_exec("
                };

                for (String pattern : maliciousPatterns)
                {
                    if (headerStr.contains(pattern))
                    {
                        throw new InvalidExtensionException(new String[]{"safe"},
                            FileTypeUtils.getExtension(file), file.getOriginalFilename());
                    }
                }
            }
        }
    }

    private static final File getAbsoluteFile(String uploadDir, String fileName) throws IOException
    {
        File desc = new File(uploadDir + File.separator + fileName);

        if (!desc.exists())
        {
            if (!desc.getParentFile().exists())
            {
                desc.getParentFile().mkdirs();
            }
        }
        return desc.isAbsolute() ? desc : desc.getAbsoluteFile();
    }

    private static final String getPathFileName(String fileName) throws IOException
    {
        String pathFileName = "/" + fileName;
        return pathFileName;
    }

    /**
     * 文件大小校验
     *
     * @param file 上传的文件
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException 文件校验异常
     */
    public static final void assertAllowed(MultipartFile file, String[] allowedExtension)
            throws FileSizeLimitExceededException, InvalidExtensionException
    {
        long size = file.getSize();
        if (size > DEFAULT_MAX_SIZE)
        {
            throw new FileSizeLimitExceededException(DEFAULT_MAX_SIZE / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = FileTypeUtils.getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension))
        {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension,
                        fileName);
            }
            else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION)
            {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension,
                        fileName);
            }
            else
            {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }

    /**
     * 判断MIME类型是否是允许的MIME类型
     *
     * @param extension 上传文件类型
     * @param allowedExtension 允许上传文件类型
     * @return true/false
     */
    public static final boolean isAllowedExtension(String extension, String[] allowedExtension)
    {
        for (String str : allowedExtension)
        {
            if (str.equalsIgnoreCase(extension))
            {
                return true;
            }
        }
        return false;
    }
}